// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AppNotificationAdapter extends TypeAdapter<AppNotification> {
  @override
  final int typeId = 5;

  @override
  AppNotification read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AppNotification()
      ..id = fields[0] as String
      ..title = fields[1] as String
      ..message = fields[2] as String
      ..type = fields[3] as String
      ..createdAt = fields[4] as DateTime
      ..isRead = fields[5] as bool
      ..userId = fields[6] as String
      ..relatedId = fields[7] as String?
      ..actionType = fields[8] as String?
      ..scheduledFor = fields[9] as DateTime?
      ..isActive = fields[10] as bool;
  }

  @override
  void write(BinaryWriter writer, AppNotification obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.message)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.isRead)
      ..writeByte(6)
      ..write(obj.userId)
      ..writeByte(7)
      ..write(obj.relatedId)
      ..writeByte(8)
      ..write(obj.actionType)
      ..writeByte(9)
      ..write(obj.scheduledFor)
      ..writeByte(10)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppNotificationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
